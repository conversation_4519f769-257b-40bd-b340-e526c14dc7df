import { Loading } from '@/shared/components';
import MainLayout from '@/shared/layouts/MainLayout';
import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import UserDatasetManagementPage from '../pages/UserDatasetManagementPage';
import i18n from '@/lib/i18n';
import { CreateDatasetGooglePage, CreateDatasetOpenAIPage } from '../pages';

// Import User Dataset module pages
const DataFineTunePage = lazy(() => import('@/modules/user-dataset/pages/DataFineTunePage'));

/**
 * User Dataset module routes
 */
const userDatasetRoutes: RouteObject[] = [
  {
    path: '/user-dataset',
    element: (
      <MainLayout title={i18n.t('user-dataset:title', 'Quản lý Dataset & Model')}>
        <Suspense fallback={<Loading />}>
          <UserDatasetManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/user-dataset/data-fine-tune',
    element: (
      <MainLayout title={i18n.t('user-dataset:dataFineTune.title', 'Dataset Fine-tune')}>
        <Suspense fallback={<Loading />}>
          <DataFineTunePage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/user-dataset/create-openai',
    element: (
      <MainLayout title={i18n.t('user-dataset:createDataset.openai.title', 'Tạo Dataset OpenAI')}>
        <Suspense fallback={<Loading />}>
          <CreateDatasetOpenAIPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/user-dataset/create-google',
    element: (
      <MainLayout title={i18n.t('user-dataset:createDataset.google.title', 'Tạo Dataset Google')}>
        <Suspense fallback={<Loading />}>
          <CreateDatasetGooglePage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default userDatasetRoutes;

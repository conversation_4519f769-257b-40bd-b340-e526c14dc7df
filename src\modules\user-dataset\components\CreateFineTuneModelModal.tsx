import React, { useState, useEffect, useCallback } from 'react';
import {
  Button,
  Input,
  Typography,
  Textarea,
  FormItem,
  Slider,

} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm/SlideInForm';
import { useTranslation } from 'react-i18next';

import { getUserDataFineTuneList } from '@/modules/user-dataset/user-data-fine-tune/services/user-data-fine-tune.service';
import {
  getActiveSystemModels,
  getUserKeyLLMList,
} from '@/modules/user-dataset/user-mode-base/services/user-mode-base.service';
import { useUserModelsByKey } from '@/modules/user-dataset/user-mode-base/hooks/useUserModeBase';
import {
  CreateFineTuneModelDto,
  HyperParameterConfig,
  HyperParameters,
} from '../user-data-fine-tune/types/fine-tune-model.types';
import { UserDataFineTuneResponseDto } from '@/modules/user-dataset/user-data-fine-tune/types/user-data-fine-tune.types';
import {
  UserModeBaseResponseDto,
  ModeBaseProvider,
  UserKeyLLMResponseDto,
  
} from '@/modules/user-dataset/user-mode-base/types/user-mode-base.types';
import { PaginatedResult } from '@/shared/types/api';
import { AsyncSelectWithPagination } from '@/shared/components/common/Select';

// Extended interface để handle response structure thực tế
interface BaseModelResponse extends UserModeBaseResponseDto {
  modelId?: string;
}

// Interface cho API response structure
interface ApiResponse {
  items?: BaseModelResponse[];
  data?: BaseModelResponse[];
  result?: BaseModelResponse[];
}

interface CreateFineTuneModelSlideFormProps {
  isVisible: boolean;
  onClose: () => void;
  onSubmit: (data: CreateFineTuneModelDto) => void;
  isLoading?: boolean;
}

/**
 * SlideInForm tạo Fine-tune Model
 * Thay thế Modal bằng SlideInForm để có UX tốt hơn
 */
const CreateFineTuneModelSlideForm: React.FC<CreateFineTuneModelSlideFormProps> = ({
  isVisible,
  onClose,
  onSubmit,
  isLoading = false,
}) => {
  const { t } = useTranslation();

  // Form state
  const [formData, setFormData] = useState<CreateFineTuneModelDto>({
    name: '',
    description: '',
    datasetId: '',
    baseModelId: '',
    provider: 'OPENAI',
    suffix: '',
    hyperparameters: {
      epochs: 3,
      batchSize: 1,
      learningRate: 0.0001,
    },
  });

  // Hyperparameter config state
  const [hyperConfig, setHyperConfig] = useState<HyperParameterConfig>({
    mode: 'auto',
  });

  // Data loading states
  const [datasets, setDatasets] = useState<UserDataFineTuneResponseDto[]>([]);
  const [baseModels, setBaseModels] = useState<BaseModelResponse[]>([]);
  const [userKeys, setUserKeys] = useState<UserKeyLLMResponseDto[]>([]);
  const [loadingData, setLoadingData] = useState(false);

  // Model source selection
  const [modelSource, setModelSource] = useState<'system' | 'user-keys'>('system');
  const [selectedKeyLLMId, setSelectedKeyLLMId] = useState<string>('');

  // Debug: Log state changes
  useEffect(() => {

  }, [selectedKeyLLMId]);

  // Hook để lấy models by key - chỉ gọi khi có selectedKeyLLMId
  const {
    data: userModelsByKeyData,
    isLoading: isLoadingUserModelsByKey,
    error: userModelsByKeyError,
    isError: isUserModelsByKeyError
  } = useUserModelsByKey(selectedKeyLLMId);

  const loadData = useCallback(async () => {
   
    setLoadingData(true);
    try {
      // Luôn load datasets
      const datasetsResponse = await getUserDataFineTuneList({ page: 1, limit: 100 });

      // Handle datasets response structure - PaginatedResult có property 'items'
      if (Array.isArray(datasetsResponse)) {
        setDatasets(datasetsResponse);
      } else {
        // Cast to PaginatedResult type
        const paginatedResponse =
          datasetsResponse as unknown as PaginatedResult<UserDataFineTuneResponseDto>;
        if (paginatedResponse?.items && Array.isArray(paginatedResponse.items)) {
          setDatasets(paginatedResponse.items);
        } else {
          setDatasets([]);
        }
      }

      // Load base models dựa trên model source
      if (modelSource === 'system') {
        const baseModelsResponse = await getActiveSystemModels(['OPENAI']);

        // Đảm bảo baseModelsResponse là array
        if (Array.isArray(baseModelsResponse)) {
          setBaseModels(baseModelsResponse);
        } else {
          const response = baseModelsResponse as ApiResponse;
          if (response?.items && Array.isArray(response.items)) {
            setBaseModels(response.items);
          } else if (response?.data && Array.isArray(response.data)) {
            setBaseModels(response.data);
          } else if (response?.result && Array.isArray(response.result)) {
            setBaseModels(response.result);
          } else {
            setBaseModels([]);
          }
        }
      } else if (modelSource === 'user-keys') {
      
        // Load user keys khi chọn user-keys mode
        const userKeysResponse = await getUserKeyLLMList({
          page: 1,
          limit: 100,
          provider: 'OPENAI',
        });

  

        // getUserKeyLLMList returns response.result, so userKeysResponse is PaginatedResult
        // Expected structure: { items: [...], meta: {...} }
        if (userKeysResponse?.items && Array.isArray(userKeysResponse.items)) {
          
          setUserKeys(userKeysResponse.items);
        } else {
        
          setUserKeys([]);
        }

        // Reset base models khi chuyển sang user-keys mode
        setBaseModels([]);
      }
    } catch (error) {
      console.error('Error loading data:', error);
      setBaseModels([]);
    } finally {
      setLoadingData(false);
    }
  }, [modelSource]);

  // Update base models when user models by key data changes
  useEffect(() => {
 

    if (modelSource === 'user-keys' && selectedKeyLLMId) {


      if (isLoadingUserModelsByKey) {
   
        return; // Don't update state while loading
      }

      if (isUserModelsByKeyError) {
        console.error('❌ Error loading models for key:', selectedKeyLLMId, userModelsByKeyError);
        setBaseModels([]);
        return;
      }

      if (userModelsByKeyData && Array.isArray(userModelsByKeyData.items)) {
        const modelsArray = userModelsByKeyData.items;
      
        const convertedModels: BaseModelResponse[] = modelsArray.map(model => ({
          ...model,
          provider: model.provider as ModeBaseProvider,
          usageCount: 0,
          averageRating: 0,
          modelId: model.name,
        }));
      
        setBaseModels(convertedModels);
      } else {
        setBaseModels([]);
      }
      
    } else if (modelSource === 'user-keys' && !selectedKeyLLMId) {

      setBaseModels([]);
    }
  }, [modelSource, selectedKeyLLMId, userModelsByKeyData, isLoadingUserModelsByKey, isUserModelsByKeyError, userModelsByKeyError]);

  // Load datasets and base models when slide form opens
  useEffect(() => {
    if (isVisible) {
      loadData();
    }
  }, [isVisible, modelSource, loadData]);

  // Handle form field changes
  const handleInputChange = (field: keyof CreateFineTuneModelDto, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle hyperparameter mode change
  const handleHyperModeChange = (mode: 'auto' | 'custom') => {
    setHyperConfig(prev => ({
      ...prev,
      mode,
    }));

    if (mode === 'auto') {
      // Reset to default values
      setFormData(prev => ({
        ...prev,
        hyperparameters: {
          epochs: 3,
          batchSize: 1,
          learningRate: 0.0001,
        },
      }));
    }
  };

  // Handle custom hyperparameter changes
  const handleHyperParameterChange = (field: keyof HyperParameters, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      hyperparameters: {
        ...prev.hyperparameters,
        [field]: value,
      },
    }));
  };

  // Handle model source change
  const handleModelSourceChange = (source: 'system' | 'user-keys') => {

    setModelSource(source);
    setSelectedKeyLLMId(''); // Reset selected key
    setFormData(prev => ({ ...prev, baseModelId: '' })); // Reset selected model
  
  };

  // Handle key LLM selection
  const handleKeyLLMChange = (keyLLMId: string) => {

  
    setSelectedKeyLLMId(keyLLMId);
    setFormData(prev => ({ ...prev, baseModelId: '' })); // Reset selected model
    setBaseModels([]); // Clear current models immediately



    // Force re-render để đảm bảo state được cập nhật
    setTimeout(() => {
    
    }, 100);
  };

  // Handle form submission
  const handleSubmit = () => {
    onSubmit(formData);
  };

  // Reset form when modal closes
  const handleClose = () => {
    setFormData({
      name: '',
      description: '',
      datasetId: '',
      baseModelId: '',
      provider: 'OPENAI',
      suffix: '',
      hyperparameters: {
        epochs: 3,
        batchSize: 1,
        learningRate: 0.0001,
      },
    });
    setHyperConfig({ mode: 'auto' });
    onClose();
  };

  // Validation
  const isFormValid = formData.name && formData.datasetId && formData.baseModelId;

  return (
    <SlideInForm isVisible={isVisible}>
      <div className="space-y-6">
        {/* Basic Information */}
        <div className="space-y-4">
          <Input
            label={t('Tên model')}
            value={formData.name}
            onChange={e => handleInputChange('name', e.target.value)}
            placeholder={t('Nhập tên model')}
            required
            fullWidth
          />

          <FormItem label={t('Mô tả')}>
            <Textarea
              value={formData.description}
              onChange={e => handleInputChange('description', e.target.value)}
              placeholder={t('Nhập mô tả model')}
              rows={3}
              fullWidth
            />
          </FormItem>
        </div>

        {/* Dataset Selection */}
        <div className="space-y-4">
          <div className="space-y-2">
            <label className="block text-sm font-medium mb-1 text-foreground">
              {t('Dataset')}
            </label>
            <AsyncSelectWithPagination
              value={formData.datasetId}
              onChange={(value) => {
                handleInputChange('datasetId', value as string);
              }}
              loadOptions={async ({ page, limit }) => {
                // Nếu đang loading data ban đầu, trả về empty
                if (loadingData) {
                  return {
                    items: [],
                    totalItems: 0,
                    totalPages: 0,
                    currentPage: 1,
                  };
                }

                // Pagination logic cho datasets
                const startIndex = ((page || 1) - 1) * (limit || 20);
                const endIndex = startIndex + (limit || 20);
                const paginatedDatasets = datasets.slice(startIndex, endIndex);

                // Convert to SelectOption format
                const items = paginatedDatasets.map(dataset => ({
                  value: dataset.id,
                  label: dataset.name || `Dataset ${dataset.id}`,
                  data: dataset as Record<string, unknown>,
                }));

                return {
                  items,
                  totalItems: datasets.length,
                  totalPages: Math.ceil(datasets.length / (limit || 20)),
                  currentPage: page || 1,
                };
              }}
              disabled={loadingData}
              placeholder={t('-- Chọn dataset để fine-tune --')}
              fullWidth
              size="md"
              autoLoadInitial
              debounceTime={300}
              itemsPerPage={20}
              loadingMessage={t('Đang tải datasets...')}
              noOptionsMessage={t('Không có dataset nào')}
              className="w-full"
            />
          </div>
        </div>

        {/* Model Source Selection */}
        <div className="space-y-4">
          <Typography variant="subtitle2" className="font-medium">
            {t('Nguồn Base Model')}
          </Typography>
          <div className="flex space-x-4">
            <Button
              variant={modelSource === 'system' ? 'primary' : 'outline'}
              onClick={() => handleModelSourceChange('system')}
              className="flex-1"
            >
              {t('System Models')}
            </Button>
            <Button
              variant={modelSource === 'user-keys' ? 'primary' : 'outline'}
              onClick={() => handleModelSourceChange('user-keys')}
              className="flex-1"
            >
              {t('User Models (Keys)')}
            </Button>
          </div>
        </div>

        {/* User Key Selection - Chỉ hiển thị khi chọn user-keys */}
        {modelSource === 'user-keys' && (
          <div className="space-y-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium mb-1 text-foreground">
                {t('Chọn User Key LLM')}
              </label>
          
              <AsyncSelectWithPagination
                    value={selectedKeyLLMId}
                    onChange={(value) => {
                      handleKeyLLMChange(value as string);
                    }}
                    loadOptions={async ({ page, limit }) => {
                      // Nếu đang loading data ban đầu, trả về empty
                      if (loadingData) {
                        return {
                          items: [],
                          totalItems: 0,
                          totalPages: 0,
                          currentPage: 1,
                        };
                      }

                      // Pagination logic - đã loại bỏ chức năng search
                      const startIndex = ((page || 1) - 1) * (limit || 20);
                      const endIndex = startIndex + (limit || 20);
                      const paginatedKeys = userKeys.slice(startIndex, endIndex);

                      // Convert to SelectOption format
                      const items = paginatedKeys.map(key => ({
                        value: key.id,
                        label: key.name || `Key LLM ID:(${key.id})`,
                        data: key as Record<string, unknown>,
                      }));

                      return {
                        items,
                        totalItems: userKeys.length,
                        totalPages: Math.ceil(userKeys.length / (limit || 20)),
                        currentPage: page || 1,
                      };
                    }}
                    disabled={loadingData}
                    placeholder={loadingData ? t('Đang tải danh sách keys...') : t('-- Chọn User Key --')}
                    fullWidth
                    size="md"

                    autoLoadInitial
                    debounceTime={300}
                    itemsPerPage={20}
                    loadingMessage={t('Đang tải danh sách keys...')}
                    noOptionsMessage={t('Không có User Key nào')}
                    className="w-full"
                  />
            </div>

         

            {loadingData && (
              <Typography variant="caption" className="text-blue-500">
                {t('Đang tải danh sách User Keys...')}
              </Typography>
            )}
            {!loadingData && userKeys?.length === 0 && (
              <Typography variant="caption" className="text-orange-500">
                {t('Không có User Key LLM nào khả dụng')}
              </Typography>
            )}


          </div>
        )}

        {/* Base Model Selection */}
        <div className="space-y-4">
          <div className="space-y-2">
            <label className="block text-sm font-medium mb-1 text-foreground">
              {t('Base Model')}
            </label>

            <AsyncSelectWithPagination
              value={formData.baseModelId}
              onChange={(value) => {
                handleInputChange('baseModelId', value as string);
              }}
              loadOptions={async ({ page, limit }) => {
                // Nếu đang loading data ban đầu hoặc loading models từ key, trả về empty
                if (loadingData || (modelSource === 'user-keys' && isLoadingUserModelsByKey)) {
                  return {
                    items: [],
                    totalItems: 0,
                    totalPages: 0,
                    currentPage: 1,
                  };
                }

                // Nếu chọn user-keys nhưng chưa chọn key, trả về empty
                if (modelSource === 'user-keys' && !selectedKeyLLMId) {
                  return {
                    items: [],
                    totalItems: 0,
                    totalPages: 0,
                    currentPage: 1,
                  };
                }

                // Pagination logic cho base models
                const startIndex = ((page || 1) - 1) * (limit || 20);
                const endIndex = startIndex + (limit || 20);
                const paginatedModels = baseModels.slice(startIndex, endIndex);

                // Convert to SelectOption format
                const items = paginatedModels.map(model => ({
                  value: model.id,
                  label: model.modelId || model.name || `Model ${model.id}`,
                  data: model as Record<string, unknown>,
                }));

                return {
                  items,
                  totalItems: baseModels.length,
                  totalPages: Math.ceil(baseModels.length / (limit || 20)),
                  currentPage: page || 1,
                };
              }}
              disabled={
                (modelSource === 'system' && loadingData) ||
                (modelSource === 'user-keys' && (!selectedKeyLLMId || isLoadingUserModelsByKey))
              }
              placeholder={
                modelSource === 'system'
                  ? loadingData
                    ? t('Đang tải system models...')
                    : t('-- Chọn system model --')
                  : selectedKeyLLMId
                    ? isLoadingUserModelsByKey
                      ? t('Đang tải models từ key...')
                      : t('-- Chọn user model --')
                    : t('Vui lòng chọn key LLM trước')
              }
              fullWidth
              size="md"
              autoLoadInitial
              debounceTime={300}
              itemsPerPage={20}
              loadingMessage={
                modelSource === 'system'
                  ? t('Đang tải system models...')
                  : t('Đang tải models từ key...')
              }
              noOptionsMessage={
                modelSource === 'user-keys' && !selectedKeyLLMId
                  ? t('Vui lòng chọn key LLM trước')
                  : t('Không có model nào khả dụng')
              }
              className="w-full"
            />


          </div>
       


          {/* Status messages */}
          {modelSource === 'user-keys' && !selectedKeyLLMId && (
            <Typography variant="caption" className="text-gray-500">
              {t('Chọn User Key LLM để xem danh sách models có sẵn')}
            </Typography>
          )}

          {modelSource === 'user-keys' && selectedKeyLLMId && isLoadingUserModelsByKey && (
            <Typography variant="caption" className="text-blue-500">
              {t('Đang tải models từ key đã chọn...')}
            </Typography>
          )}

          {modelSource === 'user-keys' &&
            selectedKeyLLMId &&
            !isLoadingUserModelsByKey &&
            baseModels.length === 0 && (
              <Typography variant="caption" className="text-orange-500">
                {t('Không có models nào khả dụng cho key này')}
              </Typography>
            )}

          {modelSource === 'user-keys' &&
            selectedKeyLLMId &&
            !isLoadingUserModelsByKey &&
            baseModels.length > 0 && (
              <Typography variant="caption" className="text-green-600">
                {t('Tìm thấy {{count}} models từ key này', { count: baseModels.length })}
              </Typography>
            )}

          {modelSource === 'user-keys' &&
            selectedKeyLLMId &&
            !isLoadingUserModelsByKey &&
            isUserModelsByKeyError && (
              <Typography variant="caption" className="text-red-500">
                {t('Lỗi khi tải models: {{error}}', {
                  error: userModelsByKeyError?.message || 'Không thể tải models từ key này'
                })}
              </Typography>
            )}
        </div>

        {/* Suffix */}
        <div className="space-y-4">
          <Input
            label={t('Suffix')}
            value={formData.suffix}
            onChange={e => handleInputChange('suffix', e.target.value)}
            placeholder={t('Nhập suffix cho model')}
            fullWidth
          />
        </div>

        {/* Hyperparameters Configuration */}
        <div className="space-y-4">
          <Typography variant="h6">{t('Cấu hình Hyperparameters')}</Typography>

          <div className="flex space-x-4">
            <Button
              variant={hyperConfig.mode === 'auto' ? 'primary' : 'outline'}
              onClick={() => handleHyperModeChange('auto')}
            >
              {t('Auto')}
            </Button>
            <Button
              variant={hyperConfig.mode === 'custom' ? 'primary' : 'outline'}
              onClick={() => handleHyperModeChange('custom')}
            >
              {t('Custom')}
            </Button>
          </div>

          {hyperConfig.mode === 'custom' && (
            <div className="space-y-4 p-4 border rounded-lg bg-gray-50 dark:bg-gray-800 mt-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('Epochs')}
                </label>
                <Slider
                  value={formData.hyperparameters.epochs}
                  min={1}
                  max={10}
                  step={1}
                  onValueChange={value => handleHyperParameterChange('epochs', value)}
                  showValue={true}
                  valuePrefix=""
                  valueSuffix=" epochs"
                  className="mb-2"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('Batch Size')}
                </label>
                <Slider
                  value={formData.hyperparameters.batchSize as number}
                  min={1}
                  max={32}
                  step={1}
                  onValueChange={value => handleHyperParameterChange('batchSize', value)}
                  showValue={true}
                  valuePrefix=""
                  valueSuffix=""
                  className="mb-2"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('Learning Rate')}
                </label>
                <Slider
                  value={formData.hyperparameters.learningRate}
                  min={0.0001}
                  max={0.01}
                  step={0.0001}
                  onValueChange={value => handleHyperParameterChange('learningRate', value)}
                  showValue={true}
                  valuePrefix=""
                  valueSuffix=""
                  className="mb-2"
                />
              </div>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3 pt-6 mt-6 border-t">
          <Button variant="outline" onClick={handleClose}>
            {t('Hủy')}
          </Button>
          <Button
            variant="primary"
            onClick={handleSubmit}
            disabled={!isFormValid || isLoading || loadingData}
            isLoading={isLoading}
          >
            {t('Tạo Model')}
          </Button>
        </div>
      </div>
    </SlideInForm>
  );
};

export default CreateFineTuneModelSlideForm;

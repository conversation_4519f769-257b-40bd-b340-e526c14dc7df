import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Typo<PERSON>, 
  <PERSON>ton, 
  Table, 
  TableHeader, 
  TableBody, 
  TableRow, 
  TableHead, 
  TableCell,
  Card,
  CardContent,
  Badge,
  Spinner
} from '@/shared/components/common';
import { AsyncSelectWithPagination } from '@/shared/components/common/Select';
import { 
  getUserKeyLLMList,
  getUserModelsByKey,
  getActiveSystemModels,
  getUserFineTuneModels
} from '../user-mode-base/services/user-mode-base.service';
import {
  UserKeyLLMResponseDto,
  UserModelsByKeyResponseDto,
  UserModeBaseResponseDto,
  PaginatedResult
} from '../user-mode-base/types/user-mode-base.types';
import { UserModelFineTuneResponseDto } from '../user-mode-fine-tune/types/user-model-fine-tune.types';

type TabType = 'user-keys' | 'user-models' | 'system-models' | 'fine-tune-models';

interface ApiIntegrationPageProps {}

const ApiIntegrationPage: React.FC<ApiIntegrationPageProps> = () => {
  const { t } = useTranslation(['common']);
  
  // State cho tab hiện tại
  const [activeTab, setActiveTab] = useState<TabType>('user-keys');
  
  // State cho loading
  const [loading, setLoading] = useState(false);
  
  // State cho dữ liệu
  const [userKeys, setUserKeys] = useState<UserKeyLLMResponseDto[]>([]);
  const [userModels, setUserModels] = useState<UserModelsByKeyResponseDto[]>([]);
  const [systemModels, setSystemModels] = useState<UserModeBaseResponseDto[]>([]);
  const [fineTuneModels, setFineTuneModels] = useState<UserModelFineTuneResponseDto[]>([]);
  
  // State cho User Models by Key
  const [selectedKeyId, setSelectedKeyId] = useState<string>('');
  const [availableKeys, setAvailableKeys] = useState<UserKeyLLMResponseDto[]>([]);
  
  // State cho pagination
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  });

  // Load User Keys khi component mount hoặc khi cần cho User Models tab
  const loadUserKeys = async () => {
    try {
      setLoading(true);
      const result = await getUserKeyLLMList({ page: 1, limit: 100 });
      setUserKeys(result.data);
      setAvailableKeys(result.data); // Dùng cho dropdown
      setPagination({
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages
      });
    } catch (error) {
      console.error('Error loading user keys:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load User Models by Key
  const loadUserModelsByKey = async (keyId: string) => {
    if (!keyId) return;
    
    try {
      setLoading(true);
      const result = await getUserModelsByKey(keyId);
      setUserModels(result);
    } catch (error) {
      console.error('Error loading user models by key:', error);
      setUserModels([]);
    } finally {
      setLoading(false);
    }
  };

  // Load Active System Models
  const loadSystemModels = async () => {
    try {
      setLoading(true);
      const result = await getActiveSystemModels(['OPENAI', 'ANTHROPIC', 'GOOGLE']);
      setSystemModels(result);
    } catch (error) {
      console.error('Error loading system models:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load User Fine-Tune Models
  const loadFineTuneModels = async () => {
    try {
      setLoading(true);
      const result = await getUserFineTuneModels();
      setFineTuneModels(result);
    } catch (error) {
      console.error('Error loading fine-tune models:', error);
    } finally {
      setLoading(false);
    }
  };

  // Handle tab change
  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    
    switch (tab) {
      case 'user-keys':
        loadUserKeys();
        break;
      case 'user-models':
        // Load available keys first if not loaded
        if (availableKeys.length === 0) {
          loadUserKeys();
        }
        break;
      case 'system-models':
        loadSystemModels();
        break;
      case 'fine-tune-models':
        loadFineTuneModels();
        break;
    }
  };

  // Handle key selection for User Models tab
  const handleKeySelection = (keyId: string) => {
    setSelectedKeyId(keyId);
    loadUserModelsByKey(keyId);
  };

  // Load initial data
  useEffect(() => {
    loadUserKeys();
  }, []);

  // Render User Keys Table
  const renderUserKeysTable = () => (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>{t('ID')}</TableHead>
          <TableHead>{t('Tên')}</TableHead>
          <TableHead>{t('Provider')}</TableHead>
          <TableHead>{t('Trạng thái')}</TableHead>
          <TableHead>{t('Ngày tạo')}</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {userKeys.map((key) => (
          <TableRow key={key.id}>
            <TableCell className="font-mono text-sm">{key.id}</TableCell>
            <TableCell>{key.name}</TableCell>
            <TableCell>
              <Badge variant="outline">{key.provider}</Badge>
            </TableCell>
            <TableCell>
              <Badge variant={key.status === 'ACTIVE' ? 'success' : 'secondary'}>
                {key.status}
              </Badge>
            </TableCell>
            <TableCell>
              {new Date(key.createdAt).toLocaleDateString('vi-VN')}
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );

  // Render User Models Table
  const renderUserModelsTable = () => (
    <div className="overflow-x-auto">
      <table className="w-full border-collapse">
        <thead>
          <tr className="border-b">
            <th className="text-left p-3 font-medium">{t('ID')}</th>
            <th className="text-left p-3 font-medium">{t('Tên')}</th>
            <th className="text-left p-3 font-medium">{t('Tên hiển thị')}</th>
            <th className="text-left p-3 font-medium">{t('Provider')}</th>
            <th className="text-left p-3 font-medium">{t('Loại')}</th>
            <th className="text-left p-3 font-medium">{t('Trạng thái')}</th>
            <th className="text-left p-3 font-medium">{t('Max Tokens')}</th>
            <th className="text-left p-3 font-medium">{t('Chi phí Input')}</th>
            <th className="text-left p-3 font-medium">{t('Chi phí Output')}</th>
          </tr>
        </thead>
        <tbody>
          {userModels.map((model, index) => (
            <tr key={model.id || index} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
              <td className="p-3 font-mono text-sm">{model.id}</td>
              <td className="p-3">{model.name}</td>
              <td className="p-3">{model.displayName}</td>
              <td className="p-3">
                <Badge variant="info">{model.provider}</Badge>
              </td>
              <td className="p-3">{model.type}</td>
              <td className="p-3">
                <Badge variant={model.status === 'ACTIVE' ? 'success' : 'secondary'}>
                  {model.status}
                </Badge>
              </td>
              <td className="p-3 text-right">{model.maxTokens?.toLocaleString() || 'N/A'}</td>
              <td className="p-3 text-right">${model.costPerInputToken || 'N/A'}</td>
              <td className="p-3 text-right">${model.costPerOutputToken || 'N/A'}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  // Render User Models Tab Content
  const renderUserModelsTabContent = () => (
    <>
      <div className="mb-4">
        <label className="block text-sm font-medium mb-2">
          {t('Chọn User Key để xem Models')}
        </label>
        <AsyncSelectWithPagination
          value={selectedKeyId}
          onChange={(value) => handleKeySelection(value as string)}
          loadOptions={async ({ page, limit }) => {
            const startIndex = ((page || 1) - 1) * (limit || 20);
            const endIndex = startIndex + (limit || 20);
            const paginatedKeys = availableKeys.slice(startIndex, endIndex);

            const items = paginatedKeys.map(key => ({
              value: key.id,
              label: key.name || `Key ${key.id}`,
              data: key as Record<string, unknown>,
            }));

            return {
              items,
              totalItems: availableKeys.length,
              totalPages: Math.ceil(availableKeys.length / (limit || 20)),
              currentPage: page || 1,
            };
          }}
          placeholder={t('-- Chọn User Key --')}
          fullWidth
          size="md"
          autoLoadInitial
          itemsPerPage={20}
          className="max-w-md"
        />
      </div>
    </>
  );

  // Render System Models Table
  const renderSystemModelsTable = () => (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>{t('Model ID')}</TableHead>
          <TableHead>{t('Tên')}</TableHead>
          <TableHead>{t('Provider')}</TableHead>
          <TableHead>{t('Loại')}</TableHead>
          <TableHead>{t('Chi phí Input')}</TableHead>
          <TableHead>{t('Chi phí Output')}</TableHead>
          <TableHead>{t('Context Length')}</TableHead>
          <TableHead>{t('Trạng thái')}</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {systemModels.map((model) => (
          <TableRow key={model.id}>
            <TableCell className="font-mono text-sm">{model.modelId}</TableCell>
            <TableCell>{model.name || model.modelId}</TableCell>
            <TableCell>
              <Badge variant="outline">{model.provider}</Badge>
            </TableCell>
            <TableCell>{model.type}</TableCell>
            <TableCell className="text-right">
              {model.inputCost ? `$${model.inputCost}` : 'N/A'}
            </TableCell>
            <TableCell className="text-right">
              {model.outputCost ? `$${model.outputCost}` : 'N/A'}
            </TableCell>
            <TableCell className="text-right">
              {model.contextLength ? model.contextLength.toLocaleString() : 'N/A'}
            </TableCell>
            <TableCell>
              <Badge variant={model.status === 'ACTIVE' ? 'success' : 'secondary'}>
                {model.status}
              </Badge>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );

  // Render Fine-Tune Models Table
  const renderFineTuneModelsTable = () => (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>{t('ID')}</TableHead>
          <TableHead>{t('Tên Model')}</TableHead>
          <TableHead>{t('Base Model')}</TableHead>
          <TableHead>{t('Dataset ID')}</TableHead>
          <TableHead>{t('Trạng thái')}</TableHead>
          <TableHead>{t('Provider Model ID')}</TableHead>
          <TableHead>{t('Ngày tạo')}</TableHead>
          <TableHead>{t('Ngày hoàn thành')}</TableHead>
          <TableHead>{t('Lỗi')}</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {fineTuneModels.map((model) => (
          <TableRow key={model.id}>
            <TableCell className="font-mono text-sm">{model.id}</TableCell>
            <TableCell>{model.name}</TableCell>
            <TableCell>{model.baseModelName}</TableCell>
            <TableCell className="font-mono text-sm">{model.datasetId}</TableCell>
            <TableCell>
              <Badge
                variant={
                  model.status === 'SUCCEEDED' ? 'success' :
                  model.status === 'FAILED' ? 'danger' :
                  model.status === 'RUNNING' ? 'warning' : 'secondary'
                }
              >
                {model.status}
              </Badge>
            </TableCell>
            <TableCell className="font-mono text-sm">
              {model.providerModelId || 'N/A'}
            </TableCell>
            <TableCell>
              {new Date(model.createdAt).toLocaleDateString('vi-VN')}
            </TableCell>
            <TableCell>
              {model.finishedAt
                ? new Date(model.finishedAt).toLocaleDateString('vi-VN')
                : 'N/A'
              }
            </TableCell>
            <TableCell className="max-w-xs truncate">
              {model.error || 'N/A'}
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <Typography variant="h4" className="mb-2">
          {t('API Integration Dashboard')}
        </Typography>
        <Typography variant="body2" className="text-muted-foreground">
          {t('Quản lý và xem dữ liệu từ các API khác nhau')}
        </Typography>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-2 mb-6 border-b">
        <Button
          variant={activeTab === 'user-keys' ? 'primary' : 'ghost'}
          onClick={() => handleTabChange('user-keys')}
          className="rounded-b-none"
        >
          {t('User Keys')}
        </Button>
        <Button
          variant={activeTab === 'user-models' ? 'primary' : 'ghost'}
          onClick={() => handleTabChange('user-models')}
          className="rounded-b-none"
        >
          {t('User Models')}
        </Button>
        <Button
          variant={activeTab === 'system-models' ? 'primary' : 'ghost'}
          onClick={() => handleTabChange('system-models')}
          className="rounded-b-none"
        >
          {t('System Models')}
        </Button>
        <Button
          variant={activeTab === 'fine-tune-models' ? 'primary' : 'ghost'}
          onClick={() => handleTabChange('fine-tune-models')}
          className="rounded-b-none"
        >
          {t('Fine-Tune Models')}
        </Button>
      </div>

      {/* Content Area */}
      <Card>
        <CardContent className="p-6">
          {/* Header with count */}
          {!loading && (
            <div className="mb-4 flex justify-between items-center">
              <Typography variant="h6">
                {activeTab === 'user-keys' && `${t('User Keys')} (${userKeys.length})`}
                {activeTab === 'user-models' && `${t('User Models')} (${userModels.length})`}
                {activeTab === 'system-models' && `${t('System Models')} (${systemModels.length})`}
                {activeTab === 'fine-tune-models' && `${t('Fine-Tune Models')} (${fineTuneModels.length})`}
              </Typography>
              <Button
                variant="outline"
                onClick={() => handleTabChange(activeTab)}
                size="sm"
              >
                {t('Làm mới')}
              </Button>
            </div>
          )}

          {loading && (
            <div className="flex justify-center items-center py-8">
              <Spinner size="lg" />
              <span className="ml-2">{t('Đang tải dữ liệu...')}</span>
            </div>
          )}

          {/* Empty States */}
          {!loading && activeTab === 'user-keys' && userKeys.length === 0 && (
            <div className="text-center py-8">
              <Typography variant="body1" className="text-muted-foreground">
                {t('Không có User Key nào')}
              </Typography>
            </div>
          )}

          {!loading && activeTab === 'user-models' && !selectedKeyId && (
            <div className="text-center py-8">
              <Typography variant="body1" className="text-muted-foreground">
                {t('Vui lòng chọn User Key để xem Models')}
              </Typography>
            </div>
          )}

          {!loading && activeTab === 'user-models' && selectedKeyId && userModels.length === 0 && (
            <div className="text-center py-8">
              <Typography variant="body1" className="text-muted-foreground">
                {t('Không có Model nào cho Key đã chọn')}
              </Typography>
            </div>
          )}

          {!loading && activeTab === 'system-models' && systemModels.length === 0 && (
            <div className="text-center py-8">
              <Typography variant="body1" className="text-muted-foreground">
                {t('Không có System Model nào')}
              </Typography>
            </div>
          )}

          {!loading && activeTab === 'fine-tune-models' && fineTuneModels.length === 0 && (
            <div className="text-center py-8">
              <Typography variant="body1" className="text-muted-foreground">
                {t('Không có Fine-Tune Model nào')}
              </Typography>
            </div>
          )}

          {/* Tables */}
          {!loading && activeTab === 'user-keys' && userKeys.length > 0 && renderUserKeysTable()}
          {!loading && activeTab === 'user-models' && selectedKeyId && userModels.length > 0 && renderUserModelsTable()}
          {!loading && activeTab === 'system-models' && systemModels.length > 0 && renderSystemModelsTable()}
          {!loading && activeTab === 'fine-tune-models' && fineTuneModels.length > 0 && renderFineTuneModelsTable()}
        </CardContent>
      </Card>
    </div>
  );
};

export default ApiIntegrationPage;

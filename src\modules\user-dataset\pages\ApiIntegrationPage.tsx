import  { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,

} from '@/shared/components/common';
import { Table } from '@/shared/components/common/Table';
import { AsyncSelectWithPagination } from '@/shared/components/common/Select';
import {
  useUserKeyLLMList,
  useUserModelsByKey,
  useActiveSystemModels,
  useUserFineTuneModels
} from '../user-mode-base/hooks/useUserModeBase';
import {

  UserModeBaseResponseDto
} from '../user-mode-base/types/user-mode-base.types';
// import { formatDate } from '@/shared/utils/format';


type TabType = 'user-keys' | 'user-models' | 'system-models' | 'fine-tune-models';



const ApiIntegrationPage = () => {
  const { t } = useTranslation(['common']);

  // State cho tab hiện tại
  const [activeTab, setActiveTab] = useState<TabType>('user-keys');

  // State cho User Models by Key
  const [selectedKeyId, setSelectedKeyId] = useState<string>('');

  // React Query hooks
  const userKeysQuery = useUserKeyLLMList({ page: 1, limit: 100 });
  const userModelsQuery = useUserModelsByKey(selectedKeyId);
  const systemModelsQuery = useActiveSystemModels(['OPENAI']);
  const fineTuneModelsQuery = useUserFineTuneModels();

  // Extract data from queries
  const userKeys = userKeysQuery.data?.items || [];
  const userModels = userModelsQuery.data?.items || [];
  const systemModels = systemModelsQuery.data?.items || [];
  const fineTuneModels = fineTuneModelsQuery.data?.items || [];



  // Loading states
  const loading = userKeysQuery.isLoading || userModelsQuery.isLoading ||
                  systemModelsQuery.isLoading || fineTuneModelsQuery.isLoading;
  
  // Handle tab change
  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
  };

  // Handle key selection for User Models tab
  const handleKeySelection = (keyId: string) => {
    setSelectedKeyId(keyId);
  };




  // User Models Table Columns
  const userModelsColumns = [
   
 
    {
      key: 'provider',
      title: t('Provider'),
      dataIndex: 'provider',
      render: (value: unknown) => <span>{String(value)}</span>,
    },
    {
      key: 'type',
      title: t('Loại'),
      dataIndex: 'type',
    },
    {
      key: 'status',
      title: t('Trạng thái'),
      dataIndex: 'status',
      render: (value: unknown) => (
        <span >
          {String(value)}
        </span>
      ),
    },
    {
      key: 'maxTokens',
      title: t('Max Tokens'),
      dataIndex: 'maxTokens',
      render: (value: unknown) => (
        <div className="text-right">
          {value ? Number(value).toLocaleString() : 'N/A'}
        </div>
      ),
    },
    {
      key: 'costPerInputToken',
      title: t('Chi phí Input'),
      dataIndex: 'costPerInputToken',
      render: (value: unknown) => (
        <div className="text-right">
          {value ? `$${value}` : 'N/A'}
        </div>
      ),
    },
    {
      key: 'costPerOutputToken',
      title: t('Chi phí Output'),
      dataIndex: 'costPerOutputToken',
      render: (value: unknown) => (
        <div className="text-right">
          {value ? `$${value}` : 'N/A'}
        </div>
      ),
    },
  ];

  // System Models Table Columns
  const systemModelsColumns = [
  
    {
      key: 'name',
      title: t('Tên'),
      dataIndex: 'name',
      render: (value: unknown, record: UserModeBaseResponseDto) => String(value || record.modelId),
    },
    {
      key: 'provider',
      title: t('Provider'),
      dataIndex: 'provider',
      render: (value: unknown) => <span>{String(value)}</span>,
    },
    {
      key: 'type',
      title: t('Loại'),
      dataIndex: 'type',
    },
    {
      key: 'costPerInputToken',
      title: t('Chi phí Input'),
      dataIndex: 'costPerInputToken',
      render: (value: unknown) => (
        <div className="text-right">
          {value ? `$${value}` : 'N/A'}
        </div>
      ),
    },
    {
      key: 'costPerOutputToken',
      title: t('Chi phí Output'),
      dataIndex: 'costPerOutputToken',
      render: (value: unknown) => (
        <div className="text-right">
          {value ? `$${value}` : 'N/A'}
        </div>
      ),
    },
    {
      key: 'maxTokens',
      title: t('Max Tokens'),
      dataIndex: 'maxTokens',
      render: (value: unknown) => (
        <div className="text-right">
          {value ? Number(value).toLocaleString() : 'N/A'}
        </div>
      ),
    },
    {
      key: 'status',
      title: t('Trạng thái'),
      dataIndex: 'status',
      render: (value: unknown) => (
        <span >
          {String(value)}
        </span>
      ),
    },
  ];

  // Fine-Tune Models Table Columns
  const fineTuneModelsColumns = [
    {
      key: 'id',
      title: t('ID'),
      dataIndex: 'id',
      render: (value: unknown) => <span className="font-mono text-sm">{String(value)}</span>,
    },
    {
      key: 'name',
      title: t('Tên Model'),
      dataIndex: 'name',
    },
    {
      key: 'baseModelName',
      title: t('Base Model'),
      dataIndex: 'baseModelName',
    },
    {
      key: 'datasetId',
      title: t('Dataset ID'),
      dataIndex: 'datasetId',
      render: (value: unknown) => <span className="font-mono text-sm">{String(value)}</span>,
    },

    {
      key: 'providerModelId',
      title: t('Provider Model ID'),
      dataIndex: 'providerModelId',
      render: (value: unknown) => (
        <span className="font-mono text-sm">
          {value ? String(value) : 'N/A'}
        </span>
      ),
    },
    {
      key: 'createdAt',
      title: t('Ngày tạo'),
      dataIndex: 'createdAt',
      render: (value: unknown) =>
        new Date(value as number | string).toLocaleDateString('vi-VN'),
    }
   
  ];



  return (
    <div className="container mx-auto p-6">


      {/* Tab Navigation */}
      <div className="flex space-x-2 mb-6 border-b">

        <Button
          variant={activeTab === 'user-models' ? 'primary' : 'ghost'}
          onClick={() => handleTabChange('user-models')}
          className="rounded-b-none"
        >
          {t('User Models')}
        </Button>
        <Button
          variant={activeTab === 'system-models' ? 'primary' : 'ghost'}
          onClick={() => handleTabChange('system-models')}
          className="rounded-b-none"
        >
          {t('System Models')}
        </Button>
        <Button
          variant={activeTab === 'fine-tune-models' ? 'primary' : 'ghost'}
          onClick={() => handleTabChange('fine-tune-models')}
          className="rounded-b-none"
        >
          {t('Fine-Tune Models')}
        </Button>
      </div>

      {/* Content Area */}
      <Card className="p-6">
  

        {loading && (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-2">{t('Đang tải dữ liệu...')}</span>
            </div>
          )}

          {/* Empty States */}
          {!loading && activeTab === 'user-keys' && userKeys.length === 0 && (
            <div className="text-center py-8">
              <Typography variant="body1" className="text-muted-foreground">
                {t('Không có User Key nào')}
              </Typography>
            </div>
          )}

 

          {!loading && activeTab === 'user-models' && selectedKeyId && userModels.length === 0 && (
            <div className="text-center py-8">
              <Typography variant="body1" className="text-muted-foreground">
                {t('Không có Model nào cho Key đã chọn')}
              </Typography>
            </div>
          )}

          {!loading && activeTab === 'system-models' && systemModels.length === 0 && (
            <div className="text-center py-8">
              <Typography variant="body1" className="text-muted-foreground">
                {t('Không có System Model nào')}
              </Typography>
            </div>
          )}

      

     

          {activeTab === 'user-models' && (
            <>
              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">
                  {t('Chọn User Key để xem Models')}
                </label>
                <AsyncSelectWithPagination
                  value={selectedKeyId}
                  onChange={(value) => handleKeySelection(value as string)}
                  loadOptions={async ({ page, limit }) => {
                    const startIndex = ((page || 1) - 1) * (limit || 20);
                    const endIndex = startIndex + (limit || 20);
                    const paginatedKeys = userKeys.slice(startIndex, endIndex);

                    const items = paginatedKeys.map((key) => ({
                      value: key.id,
                      label: key.name || `Key ${key.id}`,
                      data: key as Record<string, unknown>,
                    }));

                    return {
                      items,
                      totalItems: userKeys.length,
                      totalPages: Math.ceil(userKeys.length / (limit || 20)),
                      currentPage: page || 1,
                    };
                  }}
                  placeholder={t('-- Chọn User Key --')}
                  fullWidth
                  size="md"
                  autoLoadInitial
                  itemsPerPage={20}
                  className="max-w-md"
                />
              </div>

              {selectedKeyId && (
                <Table
                  data={userModels}
                  columns={userModelsColumns}
                  loading={loading}
                  loadingText={t('Đang tải User Models...')}
                />
              )}
            </>
          )}

          {activeTab === 'system-models' && (
            <Table
              data={systemModels}
              columns={systemModelsColumns}
              loading={loading}
              loadingText={t('Đang tải System Models...')}
            />
          )}

          {activeTab === 'fine-tune-models' && (
            <Table
              data={fineTuneModels}
              columns={fineTuneModelsColumns}
              loading={loading}
              loadingText={t('Đang tải Fine-Tune Models...')}
            />
          )}
      </Card>
    </div>
  );
};

export default ApiIntegrationPage;

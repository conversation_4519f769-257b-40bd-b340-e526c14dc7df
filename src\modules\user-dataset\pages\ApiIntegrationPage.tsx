import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Button,
  Table,
  Card,
  Badge
} from '@/shared/components/common';
import { AsyncSelectWithPagination } from '@/shared/components/common/Select';
import { 
  getUserKeyLLMList,
  getUserModelsByKey,
  getActiveSystemModels,
  getUserFineTuneModels
} from '../user-mode-base/services/user-mode-base.service';
import {
  UserKeyLLMResponseDto,
  UserModelsByKeyResponseDto,
  UserModeBaseResponseDto,
  PaginatedResult
} from '../user-mode-base/types/user-mode-base.types';
import { UserModelFineTuneResponseDto } from '../user-mode-fine-tune/types/user-model-fine-tune.types';

type TabType = 'user-keys' | 'user-models' | 'system-models' | 'fine-tune-models';

interface ApiIntegrationPageProps {}

const ApiIntegrationPage: React.FC<ApiIntegrationPageProps> = () => {
  const { t } = useTranslation(['common']);
  
  // State cho tab hiện tại
  const [activeTab, setActiveTab] = useState<TabType>('user-keys');
  
  // State cho loading
  const [loading, setLoading] = useState(false);
  
  // State cho dữ liệu
  const [userKeys, setUserKeys] = useState<UserKeyLLMResponseDto[]>([]);
  const [userModels, setUserModels] = useState<UserModelsByKeyResponseDto[]>([]);
  const [systemModels, setSystemModels] = useState<UserModeBaseResponseDto[]>([]);
  const [fineTuneModels, setFineTuneModels] = useState<UserModelFineTuneResponseDto[]>([]);
  
  // State cho User Models by Key
  const [selectedKeyId, setSelectedKeyId] = useState<string>('');
  const [availableKeys, setAvailableKeys] = useState<UserKeyLLMResponseDto[]>([]);
  
  // Load User Keys khi component mount hoặc khi cần cho User Models tab
  const loadUserKeys = async () => {
    try {
      setLoading(true);
      const result = await getUserKeyLLMList({ page: 1, limit: 100 });
      setUserKeys(result.items || []);
      setAvailableKeys(result.items || []); // Dùng cho dropdown
    } catch (error) {
      console.error('Error loading user keys:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load User Models by Key
  const loadUserModelsByKey = async (keyId: string) => {
    if (!keyId) return;
    
    try {
      setLoading(true);
      const result = await getUserModelsByKey(keyId);
      setUserModels(result);
    } catch (error) {
      console.error('Error loading user models by key:', error);
      setUserModels([]);
    } finally {
      setLoading(false);
    }
  };

  // Load Active System Models
  const loadSystemModels = async () => {
    try {
      setLoading(true);
      const result = await getActiveSystemModels(['OPENAI', 'ANTHROPIC', 'GOOGLE']);
      setSystemModels(result);
    } catch (error) {
      console.error('Error loading system models:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load User Fine-Tune Models
  const loadFineTuneModels = async () => {
    try {
      setLoading(true);
      const result = await getUserFineTuneModels();
      setFineTuneModels(result);
    } catch (error) {
      console.error('Error loading fine-tune models:', error);
    } finally {
      setLoading(false);
    }
  };

  // Handle tab change
  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    
    switch (tab) {
      case 'user-keys':
        loadUserKeys();
        break;
      case 'user-models':
        // Load available keys first if not loaded
        if (availableKeys.length === 0) {
          loadUserKeys();
        }
        break;
      case 'system-models':
        loadSystemModels();
        break;
      case 'fine-tune-models':
        loadFineTuneModels();
        break;
    }
  };

  // Handle key selection for User Models tab
  const handleKeySelection = (keyId: string) => {
    setSelectedKeyId(keyId);
    loadUserModelsByKey(keyId);
  };

  // Load initial data
  useEffect(() => {
    loadUserKeys();
  }, []);

  // User Keys Table Columns
  const userKeysColumns = [
    {
      key: 'id',
      title: t('ID'),
      dataIndex: 'id',
      render: (value: unknown) => <span className="font-mono text-sm">{String(value)}</span>,
    },
    {
      key: 'name',
      title: t('Tên'),
      dataIndex: 'name',
    },
    {
      key: 'provider',
      title: t('Provider'),
      dataIndex: 'provider',
      render: (value: unknown) => <Badge variant="info">{String(value)}</Badge>,
    },
    {
      key: 'status',
      title: t('Trạng thái'),
      dataIndex: 'status',
      render: (value: unknown) => (
        <Badge variant={String(value) === 'ACTIVE' ? 'success' : 'secondary'}>
          {String(value)}
        </Badge>
      ),
    },
    {
      key: 'createdAt',
      title: t('Ngày tạo'),
      dataIndex: 'createdAt',
      render: (value: unknown) =>
        new Date(value as number | string).toLocaleDateString('vi-VN'),
    },
  ];

  // Render User Models Table
  const renderUserModelsTable = () => (
    <div className="overflow-x-auto">
      <table className="w-full border-collapse">
        <thead>
          <tr className="border-b">
            <th className="text-left p-3 font-medium">{t('ID')}</th>
            <th className="text-left p-3 font-medium">{t('Tên')}</th>
            <th className="text-left p-3 font-medium">{t('Tên hiển thị')}</th>
            <th className="text-left p-3 font-medium">{t('Provider')}</th>
            <th className="text-left p-3 font-medium">{t('Loại')}</th>
            <th className="text-left p-3 font-medium">{t('Trạng thái')}</th>
            <th className="text-left p-3 font-medium">{t('Max Tokens')}</th>
            <th className="text-left p-3 font-medium">{t('Chi phí Input')}</th>
            <th className="text-left p-3 font-medium">{t('Chi phí Output')}</th>
          </tr>
        </thead>
        <tbody>
          {userModels.map((model, index) => (
            <tr key={model.id || index} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
              <td className="p-3 font-mono text-sm">{model.id}</td>
              <td className="p-3">{model.name}</td>
              <td className="p-3">{model.displayName}</td>
              <td className="p-3">
                <Badge variant="info">{model.provider}</Badge>
              </td>
              <td className="p-3">{model.type}</td>
              <td className="p-3">
                <Badge variant={model.status === 'AVAILABLE' ? 'success' : 'secondary'}>
                  {model.status}
                </Badge>
              </td>
              <td className="p-3 text-right">{model.maxTokens?.toLocaleString() || 'N/A'}</td>
              <td className="p-3 text-right">${model.costPerInputToken || 'N/A'}</td>
              <td className="p-3 text-right">${model.costPerOutputToken || 'N/A'}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  // Render User Models Tab Content
  const renderUserModelsTabContent = () => (
    <>
      <div className="mb-4">
        <label className="block text-sm font-medium mb-2">
          {t('Chọn User Key để xem Models')}
        </label>
        <AsyncSelectWithPagination
          value={selectedKeyId}
          onChange={(value) => handleKeySelection(value as string)}
          loadOptions={async ({ page, limit }) => {
            const startIndex = ((page || 1) - 1) * (limit || 20);
            const endIndex = startIndex + (limit || 20);
            const paginatedKeys = availableKeys.slice(startIndex, endIndex);

            const items = paginatedKeys.map(key => ({
              value: key.id,
              label: key.name || `Key ${key.id}`,
              data: key as Record<string, unknown>,
            }));

            return {
              items,
              totalItems: availableKeys.length,
              totalPages: Math.ceil(availableKeys.length / (limit || 20)),
              currentPage: page || 1,
            };
          }}
          placeholder={t('-- Chọn User Key --')}
          fullWidth
          size="md"
          autoLoadInitial
          itemsPerPage={20}
          className="max-w-md"
        />
      </div>
    </>
  );

  // Render System Models Table
  const renderSystemModelsTable = () => (
    <div className="overflow-x-auto">
      <table className="w-full border-collapse">
        <thead>
          <tr className="border-b">
            <th className="text-left p-3 font-medium">{t('Model ID')}</th>
            <th className="text-left p-3 font-medium">{t('Tên')}</th>
            <th className="text-left p-3 font-medium">{t('Provider')}</th>
            <th className="text-left p-3 font-medium">{t('Loại')}</th>
            <th className="text-left p-3 font-medium">{t('Chi phí Input')}</th>
            <th className="text-left p-3 font-medium">{t('Chi phí Output')}</th>
            <th className="text-left p-3 font-medium">{t('Max Tokens')}</th>
            <th className="text-left p-3 font-medium">{t('Trạng thái')}</th>
          </tr>
        </thead>
        <tbody>
          {systemModels.map((model) => (
            <tr key={model.id} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
              <td className="p-3 font-mono text-sm">{model.modelId || model.name}</td>
              <td className="p-3">{model.name || model.displayName}</td>
              <td className="p-3">
                <Badge variant="info">{model.provider}</Badge>
              </td>
              <td className="p-3">{model.type}</td>
              <td className="p-3 text-right">
                {model.costPerInputToken ? `$${model.costPerInputToken}` : 'N/A'}
              </td>
              <td className="p-3 text-right">
                {model.costPerOutputToken ? `$${model.costPerOutputToken}` : 'N/A'}
              </td>
              <td className="p-3 text-right">
                {model.maxTokens ? model.maxTokens.toLocaleString() : 'N/A'}
              </td>
              <td className="p-3">
                <Badge variant={model.status === 'AVAILABLE' ? 'success' : 'secondary'}>
                  {model.status}
                </Badge>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  // Render Fine-Tune Models Table
  const renderFineTuneModelsTable = () => (
    <div className="overflow-x-auto">
      <table className="w-full border-collapse">
        <thead>
          <tr className="border-b">
            <th className="text-left p-3 font-medium">{t('ID')}</th>
            <th className="text-left p-3 font-medium">{t('Tên Model')}</th>
            <th className="text-left p-3 font-medium">{t('Base Model')}</th>
            <th className="text-left p-3 font-medium">{t('Dataset ID')}</th>
            <th className="text-left p-3 font-medium">{t('Trạng thái')}</th>
            <th className="text-left p-3 font-medium">{t('Provider Model ID')}</th>
            <th className="text-left p-3 font-medium">{t('Ngày tạo')}</th>
            <th className="text-left p-3 font-medium">{t('Ngày hoàn thành')}</th>
            <th className="text-left p-3 font-medium">{t('Lỗi')}</th>
          </tr>
        </thead>
        <tbody>
          {fineTuneModels.map((model) => (
            <tr key={model.id} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
              <td className="p-3 font-mono text-sm">{model.id}</td>
              <td className="p-3">{model.name}</td>
              <td className="p-3">{model.baseModelName}</td>
              <td className="p-3 font-mono text-sm">{model.datasetId}</td>
              <td className="p-3">
                <Badge
                  variant={
                    model.status === 'SUCCEEDED' ? 'success' :
                    model.status === 'FAILED' ? 'danger' :
                    model.status === 'RUNNING' ? 'warning' : 'secondary'
                  }
                >
                  {model.status}
                </Badge>
              </td>
              <td className="p-3 font-mono text-sm">
                {model.providerModelId || 'N/A'}
              </td>
              <td className="p-3">
                {new Date(model.createdAt).toLocaleDateString('vi-VN')}
              </td>
              <td className="p-3">
                {model.finishedAt
                  ? new Date(model.finishedAt).toLocaleDateString('vi-VN')
                  : 'N/A'
                }
              </td>
              <td className="p-3 max-w-xs truncate">
                {model.error || 'N/A'}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <Typography variant="h4" className="mb-2">
          {t('API Integration Dashboard')}
        </Typography>
        <Typography variant="body2" className="text-muted-foreground">
          {t('Quản lý và xem dữ liệu từ các API khác nhau')}
        </Typography>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-2 mb-6 border-b">
        <Button
          variant={activeTab === 'user-keys' ? 'primary' : 'ghost'}
          onClick={() => handleTabChange('user-keys')}
          className="rounded-b-none"
        >
          {t('User Keys')}
        </Button>
        <Button
          variant={activeTab === 'user-models' ? 'primary' : 'ghost'}
          onClick={() => handleTabChange('user-models')}
          className="rounded-b-none"
        >
          {t('User Models')}
        </Button>
        <Button
          variant={activeTab === 'system-models' ? 'primary' : 'ghost'}
          onClick={() => handleTabChange('system-models')}
          className="rounded-b-none"
        >
          {t('System Models')}
        </Button>
        <Button
          variant={activeTab === 'fine-tune-models' ? 'primary' : 'ghost'}
          onClick={() => handleTabChange('fine-tune-models')}
          className="rounded-b-none"
        >
          {t('Fine-Tune Models')}
        </Button>
      </div>

      {/* Content Area */}
      <Card className="p-6">
          {/* Header with count */}
          {!loading && (
            <div className="mb-4 flex justify-between items-center">
              <Typography variant="h6">
                {activeTab === 'user-keys' && `${t('User Keys')} (${userKeys.length})`}
                {activeTab === 'user-models' && `${t('User Models')} (${userModels.length})`}
                {activeTab === 'system-models' && `${t('System Models')} (${systemModels.length})`}
                {activeTab === 'fine-tune-models' && `${t('Fine-Tune Models')} (${fineTuneModels.length})`}
              </Typography>
              <Button
                variant="outline"
                onClick={() => handleTabChange(activeTab)}
                size="sm"
              >
                {t('Làm mới')}
              </Button>
            </div>
          )}

          {loading && (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-2">{t('Đang tải dữ liệu...')}</span>
            </div>
          )}

          {/* Empty States */}
          {!loading && activeTab === 'user-keys' && userKeys.length === 0 && (
            <div className="text-center py-8">
              <Typography variant="body1" className="text-muted-foreground">
                {t('Không có User Key nào')}
              </Typography>
            </div>
          )}

          {!loading && activeTab === 'user-models' && !selectedKeyId && (
            <div className="text-center py-8">
              <Typography variant="body1" className="text-muted-foreground">
                {t('Vui lòng chọn User Key để xem Models')}
              </Typography>
            </div>
          )}

          {!loading && activeTab === 'user-models' && selectedKeyId && userModels.length === 0 && (
            <div className="text-center py-8">
              <Typography variant="body1" className="text-muted-foreground">
                {t('Không có Model nào cho Key đã chọn')}
              </Typography>
            </div>
          )}

          {!loading && activeTab === 'system-models' && systemModels.length === 0 && (
            <div className="text-center py-8">
              <Typography variant="body1" className="text-muted-foreground">
                {t('Không có System Model nào')}
              </Typography>
            </div>
          )}

          {!loading && activeTab === 'fine-tune-models' && fineTuneModels.length === 0 && (
            <div className="text-center py-8">
              <Typography variant="body1" className="text-muted-foreground">
                {t('Không có Fine-Tune Model nào')}
              </Typography>
            </div>
          )}

          {/* Tables */}
          {!loading && activeTab === 'user-keys' && userKeys.length > 0 && (
            <Table data={userKeys} columns={userKeysColumns} />
          )}
          {!loading && activeTab === 'user-models' && selectedKeyId && userModels.length > 0 && renderUserModelsTable()}
          {!loading && activeTab === 'system-models' && systemModels.length > 0 && renderSystemModelsTable()}
          {!loading && activeTab === 'fine-tune-models' && fineTuneModels.length > 0 && renderFineTuneModelsTable()}
      </Card>
    </div>
  );
};

export default ApiIntegrationPage;

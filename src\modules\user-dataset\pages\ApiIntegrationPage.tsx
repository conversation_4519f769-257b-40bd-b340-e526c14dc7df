import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Badge
} from '@/shared/components/common';
import { Table } from '@/shared/components/common/Table';
import { AsyncSelectWithPagination } from '@/shared/components/common/Select';
import { 
  getUserKeyLLMList,
  getUserModelsByKey,
  getActiveSystemModels,
  getUserFineTuneModels
} from '../user-mode-base/services/user-mode-base.service';
import {
  UserKeyLLMResponseDto,
  UserModelsByKeyResponseDto,
  UserModeBaseResponseDto
} from '../user-mode-base/types/user-mode-base.types';
import { UserModelFineTuneResponseDto } from '../user-mode-fine-tune/types/user-model-fine-tune.types';
import { formatDate } from '@/shared/utils/format';

type TabType = 'user-keys' | 'user-models' | 'system-models' | 'fine-tune-models';

interface ApiIntegrationPageProps {
  // No props needed for this component


}

const ApiIntegrationPage: React.FC<ApiIntegrationPageProps> = () => {
  const { t } = useTranslation(['common']);
  
  // State cho tab hiện tại
  const [activeTab, setActiveTab] = useState<TabType>('user-keys');
  
  // State cho loading
  const [loading, setLoading] = useState(false);
  
  // State cho dữ liệu
  const [userKeys, setUserKeys] = useState<UserKeyLLMResponseDto[]>([]);
  const [userModels, setUserModels] = useState<UserModelsByKeyResponseDto[]>([]);
  const [systemModels, setSystemModels] = useState<UserModeBaseResponseDto[]>([]);
  const [fineTuneModels, setFineTuneModels] = useState<UserModelFineTuneResponseDto[]>([]);
  
  // State cho User Models by Key
  const [selectedKeyId, setSelectedKeyId] = useState<string>('');
  const [availableKeys, setAvailableKeys] = useState<UserKeyLLMResponseDto[]>([]);
  
  // Load User Keys khi component mount hoặc khi cần cho User Models tab
  const loadUserKeys = async () => {
    try {
      setLoading(true);
      const result = await getUserKeyLLMList({ page: 1, limit: 100 });
      setUserKeys(result.items || []);
      setAvailableKeys(result.items || []); // Dùng cho dropdown
    } catch (error) {
      console.error('Error loading user keys:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load User Models by Key
  const loadUserModelsByKey = async (keyId: string) => {
    if (!keyId) return;
    
    try {
      setLoading(true);
      const result = await getUserModelsByKey(keyId);
      setUserModels(result);
    } catch (error) {
      console.error('Error loading user models by key:', error);
      setUserModels([]);
    } finally {
      setLoading(false);
    }
  };

  // Load Active System Models
  const loadSystemModels = async () => {
    try {
      setLoading(true);
      const result = await getActiveSystemModels(['OPENAI']);
      setSystemModels(result);
    } catch (error) {
      console.error('Error loading system models:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load User Fine-Tune Models
  const loadFineTuneModels = async () => {
    try {
      setLoading(true);
      const result = await getUserFineTuneModels();
      setFineTuneModels(result);
    } catch (error) {
      console.error('Error loading fine-tune models:', error);
    } finally {
      setLoading(false);
    }
  };

  // Handle tab change
  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    
    switch (tab) {
      case 'user-keys':
        loadUserKeys();
        break;
      case 'user-models':
        // Load available keys first if not loaded
        if (availableKeys.length === 0) {
          loadUserKeys();
        }
        break;
      case 'system-models':
        loadSystemModels();
        break;
      case 'fine-tune-models':
        loadFineTuneModels();
        break;
    }
  };

  // Handle key selection for User Models tab
  const handleKeySelection = (keyId: string) => {
    setSelectedKeyId(keyId);
    loadUserModelsByKey(keyId);
  };

  // Load initial data
  useEffect(() => {
    loadUserKeys();
  }, []);

  // User Keys Table Columns
  const userKeysColumns = [
 
    {
      key: 'name',
      title: t('Tên'),
      dataIndex: 'name',
    },
    {
      key: 'provider',
      title: t('Provider'),
      dataIndex: 'provider',
      render: (value: unknown) =><span>
        {String(value)}
      </span>
    },
   
    {
      key: 'createdAt',
      title: t('Ngày tạo'),
      dataIndex: 'createdAt',
      render: (value: unknown) =>
      formatDate(value as number),
    },
  ];

  // User Models Table Columns
  const userModelsColumns = [
    {
      key: 'id',
      title: t('ID'),
      dataIndex: 'id',
      render: (value: unknown) => <span className="font-mono text-sm">{String(value)}</span>,
    },
    {
      key: 'name',
      title: t('Tên'),
      dataIndex: 'name',
    },
    {
      key: 'displayName',
      title: t('Tên hiển thị'),
      dataIndex: 'displayName',
    },
    {
      key: 'provider',
      title: t('Provider'),
      dataIndex: 'provider',
      render: (value: unknown) => <Badge variant="info">{String(value)}</Badge>,
    },
    {
      key: 'type',
      title: t('Loại'),
      dataIndex: 'type',
    },
    {
      key: 'status',
      title: t('Trạng thái'),
      dataIndex: 'status',
      render: (value: unknown) => (
        <Badge variant={String(value) === 'AVAILABLE' ? 'success' : 'secondary'}>
          {String(value)}
        </Badge>
      ),
    },
    {
      key: 'maxTokens',
      title: t('Max Tokens'),
      dataIndex: 'maxTokens',
      render: (value: unknown) => (
        <div className="text-right">
          {value ? Number(value).toLocaleString() : 'N/A'}
        </div>
      ),
    },
    {
      key: 'costPerInputToken',
      title: t('Chi phí Input'),
      dataIndex: 'costPerInputToken',
      render: (value: unknown) => (
        <div className="text-right">
          {value ? `$${value}` : 'N/A'}
        </div>
      ),
    },
    {
      key: 'costPerOutputToken',
      title: t('Chi phí Output'),
      dataIndex: 'costPerOutputToken',
      render: (value: unknown) => (
        <div className="text-right">
          {value ? `$${value}` : 'N/A'}
        </div>
      ),
    },
  ];

  // System Models Table Columns
  const systemModelsColumns = [
    {
      key: 'modelId',
      title: t('Model ID'),
      dataIndex: 'modelId',
      render: (value: unknown, record: UserModeBaseResponseDto) => (
        <span className="font-mono text-sm">{String(value || record.name)}</span>
      ),
    },
    {
      key: 'name',
      title: t('Tên'),
      dataIndex: 'name',
      render: (value: unknown, record: UserModeBaseResponseDto) => String(value || record.displayName),
    },
    {
      key: 'provider',
      title: t('Provider'),
      dataIndex: 'provider',
      render: (value: unknown) => <Badge variant="info">{String(value)}</Badge>,
    },
    {
      key: 'type',
      title: t('Loại'),
      dataIndex: 'type',
    },
    {
      key: 'costPerInputToken',
      title: t('Chi phí Input'),
      dataIndex: 'costPerInputToken',
      render: (value: unknown) => (
        <div className="text-right">
          {value ? `$${value}` : 'N/A'}
        </div>
      ),
    },
    {
      key: 'costPerOutputToken',
      title: t('Chi phí Output'),
      dataIndex: 'costPerOutputToken',
      render: (value: unknown) => (
        <div className="text-right">
          {value ? `$${value}` : 'N/A'}
        </div>
      ),
    },
    {
      key: 'maxTokens',
      title: t('Max Tokens'),
      dataIndex: 'maxTokens',
      render: (value: unknown) => (
        <div className="text-right">
          {value ? Number(value).toLocaleString() : 'N/A'}
        </div>
      ),
    },
    {
      key: 'status',
      title: t('Trạng thái'),
      dataIndex: 'status',
      render: (value: unknown) => (
        <Badge variant={String(value) === 'AVAILABLE' ? 'success' : 'secondary'}>
          {String(value)}
        </Badge>
      ),
    },
  ];

  // Fine-Tune Models Table Columns
  const fineTuneModelsColumns = [
    {
      key: 'id',
      title: t('ID'),
      dataIndex: 'id',
      render: (value: unknown) => <span className="font-mono text-sm">{String(value)}</span>,
    },
    {
      key: 'name',
      title: t('Tên Model'),
      dataIndex: 'name',
    },
    {
      key: 'baseModelName',
      title: t('Base Model'),
      dataIndex: 'baseModelName',
    },
    {
      key: 'datasetId',
      title: t('Dataset ID'),
      dataIndex: 'datasetId',
      render: (value: unknown) => <span className="font-mono text-sm">{String(value)}</span>,
    },
    {
      key: 'status',
      title: t('Trạng thái'),
      dataIndex: 'status',
      render: (value: unknown) => (
        <Badge
          variant={
            String(value) === 'SUCCEEDED' ? 'success' :
            String(value) === 'FAILED' ? 'danger' :
            String(value) === 'RUNNING' ? 'warning' : 'secondary'
          }
        >
          {String(value)}
        </Badge>
      ),
    },
    {
      key: 'providerModelId',
      title: t('Provider Model ID'),
      dataIndex: 'providerModelId',
      render: (value: unknown) => (
        <span className="font-mono text-sm">
          {value ? String(value) : 'N/A'}
        </span>
      ),
    },
    {
      key: 'createdAt',
      title: t('Ngày tạo'),
      dataIndex: 'createdAt',
      render: (value: unknown) =>
        new Date(value as number | string).toLocaleDateString('vi-VN'),
    },
    {
      key: 'finishedAt',
      title: t('Ngày hoàn thành'),
      dataIndex: 'finishedAt',
      render: (value: unknown) =>
        value ? new Date(value as number | string).toLocaleDateString('vi-VN') : 'N/A',
    },
   
  ];



  return (
    <div className="container mx-auto p-6">


      {/* Tab Navigation */}
      <div className="flex space-x-2 mb-6 border-b">
  
        <Button
          variant={activeTab === 'user-models' ? 'primary' : 'ghost'}
          onClick={() => handleTabChange('user-models')}
          className="rounded-b-none"
        >
          {t('User Models')}
        </Button>
        <Button
          variant={activeTab === 'system-models' ? 'primary' : 'ghost'}
          onClick={() => handleTabChange('system-models')}
          className="rounded-b-none"
        >
          {t('System Models')}
        </Button>
        <Button
          variant={activeTab === 'fine-tune-models' ? 'primary' : 'ghost'}
          onClick={() => handleTabChange('fine-tune-models')}
          className="rounded-b-none"
        >
          {t('Fine-Tune Models')}
        </Button>
      </div>

      {/* Content Area */}
      <Card className="p-6">
   
          {loading && (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-2">{t('Đang tải dữ liệu...')}</span>
            </div>
          )}

          {/* Empty States */}
          {!loading && activeTab === 'user-keys' && userKeys.length === 0 && (
            <div className="text-center py-8">
              <Typography variant="body1" className="text-muted-foreground">
                {t('Không có User Key nào')}
              </Typography>
            </div>
          )}

          {!loading && activeTab === 'user-models' && !selectedKeyId && (
            <div className="text-center py-8">
              <Typography variant="body1" className="text-muted-foreground">
                {t('Vui lòng chọn User Key để xem Models')}
              </Typography>
            </div>
          )}

          {!loading && activeTab === 'user-models' && selectedKeyId && userModels.length === 0 && (
            <div className="text-center py-8">
              <Typography variant="body1" className="text-muted-foreground">
                {t('Không có Model nào cho Key đã chọn')}
              </Typography>
            </div>
          )}

          {!loading && activeTab === 'system-models' && systemModels.length === 0 && (
            <div className="text-center py-8">
              <Typography variant="body1" className="text-muted-foreground">
                {t('Không có System Model nào')}
              </Typography>
            </div>
          )}

          {!loading && activeTab === 'fine-tune-models' && fineTuneModels.length === 0 && (
            <div className="text-center py-8">
              <Typography variant="body1" className="text-muted-foreground">
                {t('Không có Fine-Tune Model nào')}
              </Typography>
            </div>
          )}

          {/* Tables */}
          {activeTab === 'user-keys' && (
            <Table
              data={userKeys}
              columns={userKeysColumns}
              loading={loading}
              loadingText={t('Đang tải User Keys...')}
            />
          )}

          {activeTab === 'user-models' && (
            <>
              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">
                  {t('Chọn User Key để xem Models')}
                </label>
                <AsyncSelectWithPagination
                  value={selectedKeyId}
                  onChange={(value) => handleKeySelection(value as string)}
                  loadOptions={async ({ page, limit }) => {
                    const startIndex = ((page || 1) - 1) * (limit || 20);
                    const endIndex = startIndex + (limit || 20);
                    const paginatedKeys = availableKeys.slice(startIndex, endIndex);

                    const items = paginatedKeys.map(key => ({
                      value: key.id,
                      label: key.name || `Key ${key.id}`,
                      data: key as Record<string, unknown>,
                    }));

                    return {
                      items,
                      totalItems: availableKeys.length,
                      totalPages: Math.ceil(availableKeys.length / (limit || 20)),
                      currentPage: page || 1,
                    };
                  }}
                  placeholder={t('-- Chọn User Key --')}
                  fullWidth
                  size="md"
                  autoLoadInitial
                  itemsPerPage={20}
                  className="max-w-md"
                />
              </div>

              {selectedKeyId && (
                <Table
                  data={userModels}
                  columns={userModelsColumns}
                  loading={loading}
                  loadingText={t('Đang tải User Models...')}
                />
              )}
            </>
          )}

          {activeTab === 'system-models' && (
            <Table
              data={systemModels}
              columns={systemModelsColumns}
              loading={loading}
              loadingText={t('Đang tải System Models...')}
            />
          )}

          {activeTab === 'fine-tune-models' && (
            <Table
              data={fineTuneModels}
              columns={fineTuneModelsColumns}
              loading={loading}
              loadingText={t('Đang tải Fine-Tune Models...')}
            />
          )}
      </Card>
    </div>
  );
};

export default ApiIntegrationPage;
